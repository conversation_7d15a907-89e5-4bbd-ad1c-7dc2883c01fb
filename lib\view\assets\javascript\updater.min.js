jQuery(document).ready((function(a){"use strict";a(".ai1wm-purchase-add").on("click",(function(i){var t=a(this);t.attr("disabled",!0);var e=t.closest(".ai1wm-modal-dialog"),n=e.find(".ai1wm-modal-error"),d=e.attr("id").split("-").pop(),o=e.find(".ai1wm-purchase-id").val(),c=e.find(".ai1wm-update-link").val();a.ajax({url:"https://servmask.com/purchase/"+o+"/check",type:"GET",dataType:"json",dataFilter:function(a){return Ai1wm.Util.json(a)}}).done((function(i){a.ajax({url:ai1wm_updater.ajax.url,type:"POST",dataType:"json",data:{ai1wm_uuid:i.uuid,ai1wm_extension:i.extension},dataFilter:function(a){return Ai1wm.Util.json(a)}}).done((function(){window.location.hash="",a("#ai1wm-update-section-"+d).html(a("<a />").attr("href",c).text(ai1wm_locale.check_for_updates)),t.attr("disabled",!1)}))})).fail((function(){t.attr("disabled",!1),n.html(ai1wm_locale.invalid_purchase_id)})),i.preventDefault()})),a(".ai1wm-purchase-discard").on("click",(function(a){window.location.hash="",a.preventDefault()})),setTimeout((function(){a(".ai1wm-modal-dialog-purchase-id").unbind("click")}),300)}));
<?php
/**
 * Copyright (C) 2014-2025 ServMask Inc.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 *
 * Attribution: This code is part of the All-in-One WP Migration plugin, developed by
 *
 * ███████╗███████╗██████╗ ██╗   ██╗███╗   ███╗ █████╗ ███████╗██╗  ██╗
 * ██╔════╝██╔════╝██╔══██╗██║   ██║████╗ ████║██╔══██╗██╔════╝██║ ██╔╝
 * ███████╗█████╗  ██████╔╝██║   ██║██╔████╔██║███████║███████╗█████╔╝
 * ╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██║╚██╔╝██║██╔══██║╚════██║██╔═██╗
 * ███████║███████╗██║  ██║ ╚████╔╝ ██║ ╚═╝ ██║██║  ██║███████║██║  ██╗
 * ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝     ╚═╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝
 */

if ( ! defined( 'ABSPATH' ) ) {
	die( 'Kangaroos cannot jump here' );
}

// Include all the files that you want to load in here
require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'bandar' .
			DIRECTORY_SEPARATOR .
			'bandar' .
			DIRECTORY_SEPARATOR .
			'lib' .
			DIRECTORY_SEPARATOR .
			'Bandar.php';


if ( defined( 'WP_CLI' ) ) {
	require_once AI1WM_VENDOR_PATH .
				DIRECTORY_SEPARATOR .
				'servmask' .
				DIRECTORY_SEPARATOR .
				'command' .
				DIRECTORY_SEPARATOR .
				'class-ai1wm-wp-cli-command.php';
}

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'filesystem' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-directory.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'filesystem' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-file.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'filesystem' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-file-index.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'filesystem' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-file-htaccess.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'filesystem' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-file-webconfig.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'filesystem' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-file-robots.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'cron' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-cron.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'iterator' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-recursive-directory-iterator.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'iterator' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-recursive-iterator-iterator.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'filter' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-recursive-extension-filter.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'filter' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-recursive-exclude-filter.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'archiver' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-archiver.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'archiver' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-compressor.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'archiver' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-extractor.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'database' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-database.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'database' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-database-mysql.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'database' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-database-mysqli.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'database' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-database-sqlite.php';

require_once AI1WM_VENDOR_PATH .
			DIRECTORY_SEPARATOR .
			'servmask' .
			DIRECTORY_SEPARATOR .
			'database' .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-database-utility.php';

require_once AI1WM_CONTROLLER_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-main-controller.php';

require_once AI1WM_CONTROLLER_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-controller.php';

require_once AI1WM_CONTROLLER_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-controller.php';

require_once AI1WM_CONTROLLER_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-status-controller.php';

require_once AI1WM_CONTROLLER_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-backups-controller.php';

require_once AI1WM_CONTROLLER_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-updater-controller.php';

require_once AI1WM_CONTROLLER_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-feedback-controller.php';

require_once AI1WM_CONTROLLER_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-schedules-controller.php';

require_once AI1WM_CONTROLLER_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-reset-controller.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-init.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-compatibility.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-archive.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-config.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-config-file.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-enumerate-content.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-enumerate-media.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-enumerate-plugins.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-enumerate-tables.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-enumerate-themes.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-content.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-media.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-plugins.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-themes.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-database.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-database-file.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-download.php';

require_once AI1WM_EXPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-export-clean.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-upload.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-users.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-compatibility.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-validate.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-confirm.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-check-encryption.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-check-decryption-password.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-blogs.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-options.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-permalinks.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-enumerate.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-content.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-mu-plugins.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-database.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-done.php';

require_once AI1WM_IMPORT_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-import-clean.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-deprecated.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-extensions.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-compatibility.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-backups.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-updater.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-feedback.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-template.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-status.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-log.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-message.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-notification.php';

require_once AI1WM_MODEL_PATH .
			DIRECTORY_SEPARATOR .
			'class-ai1wm-handler.php';
